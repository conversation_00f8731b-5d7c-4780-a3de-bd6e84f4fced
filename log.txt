make[5]: *** [/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/scripts/Makefile.build:480: ../kernel_device_modules-6.6/drivers/input/touchscreen/jdchipset] Error 2
make[5]: *** Waiting for unfinished jobs....
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/input/touchscreen/sitronix_ts/sitronix_ts_i2c.c:40:22: error: call to undeclared function 'of_get_named_gpio_flags'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   40 |         host_if->irq_gpio = of_get_named_gpio_flags(np, "irq-gpio", 0, &host_if->irq_gpio_flags);
      |                             ^
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/input/touchscreen/sitronix_ts/sitronix_ts_i2c.c:40:22: note: did you mean 'of_get_named_gpio'?
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/include/linux/of_gpio.h:22:12: note: 'of_get_named_gpio' declared here
   22 | extern int of_get_named_gpio(const struct device_node *np,
      |            ^
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/input/touchscreen/sitronix_ts/sitronix_ts_i2c.c:326:6: error: variable 'ret' set but not used [-Werror,-Wunused-but-set-variable]
  326 |         int ret;
      |             ^
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/input/touchscreen/sitronix_ts/sitronix_ts_i2c.c:628:11: error: incompatible function pointer types initializing 'int (*)(struct i2c_client *)' with an expression of type 'int (struct i2c_client *, const struct i2c_device_id *)' [-Wincompatible-function-pointer-types]
  628 |         .probe = sitronix_ts_i2c_probe,
      |                  ^~~~~~~~~~~~~~~~~~~~~
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/input/touchscreen/sitronix_ts/sitronix_ts_i2c.c:631:12: error: incompatible function pointer types initializing 'void (*)(struct i2c_client *)' with an expression of type 'int (struct i2c_client *)' [-Wincompatible-function-pointer-types]
  631 |         .remove = sitronix_ts_i2c_remove,
      |                   ^~~~~~~~~~~~~~~~~~~~~~
4 errors generated.
make[6]: *** [/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/scripts/Makefile.build:243: ../kernel_device_modules-6.6/drivers/input/touchscreen/sitronix_ts/sitronix_ts_i2c.o] Error 1
make[6]: *** Waiting for unfinished jobs....
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/gpu/drm/mediatek/mediatek_v2/mtk_disp_vidle.c:120:13: warning: unused function 'mtk_vidle_dt_enable' [-Wunused-function]
  120 | static void mtk_vidle_dt_enable(unsigned int en)
      |             ^~~~~~~~~~~~~~~~~~~
3 warnings generated.
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/input/touchscreen/sitronix_ts/sitronix_ts_spi.c:85:11: error: no member named 'delay_usecs' in 'struct spi_transfer'
   85 |         xfers[0].delay_usecs = ST_SPI_DELAY_US;
      |         ~~~~~~~~ ^
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/input/touchscreen/sitronix_ts/sitronix_ts_spi.c:90:11: error: no member named 'delay_usecs' in 'struct spi_transfer'
   90 |         xfers[1].delay_usecs = 0;
      |         ~~~~~~~~ ^
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/input/touchscreen/sitronix_ts/sitronix_ts_spi.c:169:11: error: no member named 'delay_usecs' in 'struct spi_transfer'
  169 |         xfers[0].delay_usecs = ST_SPI_DELAY_US;
      |         ~~~~~~~~ ^
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/input/touchscreen/sitronix_ts/sitronix_ts_spi.c:174:11: error: no member named 'delay_usecs' in 'struct spi_transfer'
  174 |         xfers[1].delay_usecs = 0;
      |         ~~~~~~~~ ^
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/input/touchscreen/sitronix_ts/sitronix_ts_spi.c:259:11: error: no member named 'delay_usecs' in 'struct spi_transfer'
  259 |         xfers[0].delay_usecs = 1;
      |         ~~~~~~~~ ^
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/input/touchscreen/sitronix_ts/sitronix_ts_spi.c:264:11: error: no member named 'delay_usecs' in 'struct spi_transfer'
  264 |         xfers[1].delay_usecs = 0;
      |         ~~~~~~~~ ^
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/input/touchscreen/sitronix_ts/sitronix_ts_spi.c:299:11: error: no member named 'delay_usecs' in 'struct spi_transfer'
  299 |         xfers[0].delay_usecs = 0;
      |         ~~~~~~~~ ^
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/input/touchscreen/sitronix_ts/sitronix_ts_spi.c:444:22: error: call to undeclared function 'of_get_named_gpio_flags'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
  444 |         host_if->irq_gpio = of_get_named_gpio_flags(np, "irq-gpio", 0, &host_if->irq_gpio_flags);
      |                             ^
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/input/touchscreen/sitronix_ts/sitronix_ts_spi.c:444:22: note: did you mean 'of_get_named_gpio'?
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/include/linux/of_gpio.h:22:12: note: 'of_get_named_gpio' declared here
   22 | extern int of_get_named_gpio(const struct device_node *np,
      |            ^
8 errors generated.













/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/input/touchscreen/jdchipset/jadard_platform.c:743:12: error: incompatible function pointer types initializing 'int (*)(struct i2c_client *)' with an expression of type 'int (struct i2c_client *, const struct i2c_device_id *)' [-Wincompatible-function-pointer-types]
  743 |         .probe          = jadard_chip_common_probe,
      |                           ^~~~~~~~~~~~~~~~~~~~~~~~
/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/../kernel_device_modules-6.6/drivers/input/touchscreen/jdchipset/jadard_platform.c:744:13: error: incompatible function pointer types initializing 'void (*)(struct i2c_client *)' with an expression of type 'int (struct i2c_client *)' [-Wincompatible-function-pointer-types]
  744 |         .remove         = jadard_chip_common_remove,
      |                           ^~~~~~~~~~~~~~~~~~~~~~~~~
2 errors generated.
make[6]: *** [/media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/out_vnd/target/product/zelus8786p1_64_k66_P3000/obj/KLEAF_OBJ/bazel/output_user_root/output_base/sandbox/linux-sandbox/20/execroot/__main__/kernel-6.6/scripts/Makefile.build:243: ../kernel_device_modules-6.6/drivers/input/touchscreen/jdchipset/jadard_platform.o] Error 1


ERROR: /media/spm/f2462b43-b87e-4972-bac6-652d189685fa/code/z700/trunk/kernel/kernel_device_modules-6.6/BUILD.bazel:55:11: Building external kernel module (lto=default;notrim) @//kernel_device_modules-6.6:mgk_64_k66_modules.userdebug failed: (Exit 2): bash failed: error executing KernelModule command (from target //kernel_device_modules-6.6:mgk_64_k66_modules.userdebug) /bin/bash -c ... (remaining 1 argument skipped)