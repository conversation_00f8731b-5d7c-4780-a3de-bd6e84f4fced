ccflags-y += -I$(srctree)/drivers/input/touchscreen/sitronix_ts/
ccflags-y += -I$(srctree)/drivers/input/touchscreen/
#ccflags-y += -I$(srctree)/drivers/misc/mediatek/hwmon/include
ccflags-y += -I$(srctree)/drivers/misc/mediatek/include/mt-plat/
ccflags-y += -I$(srctree)/drivers/misc/mediatek/include/mt-plat/$(MTK_PLATFORM)/include/
#ccflags-y += -I$(srctree)/drivers/misc/mediatek/include/
#ccflags-y += -I$(srctree)/drivers/misc/mediatek/lcm/inc/

obj-$(CONFIG_TOUCHSCREEN_MTK_SITRONIS_TS)		+= sitronix_ts_driver.o
sitronix_ts_driver-objs	+= sitronix_ts.o \
	sitronix_ts_i2c.o \
	sitronix_ts_spi.o \
	sitronix_ts_utility.o \
	sitronix_ts_mt.o \
	sitronix_ts_nodes.o \
	sitronix_ts_upgrade.o \
	sitronix_ts_test.o \
	sitronix_ts_proximity.o \

